import { ComponentOption, ZRColor, VisualOptionFixed } from '../../util/types.js';
import ComponentModel from '../../model/Component.js';
import BrushTargetManager from '../helper/BrushTargetManager.js';
import { BrushCoverCreatorConfig, BrushMode, BrushCoverConfig, BrushDimensionMinMax, BrushAreaRange, BrushTypeUncertain, BrushType } from '../helper/BrushController.js';
import { ModelFinderObject } from '../../util/model.js';
/**
 * The input to define brush areas.
 * (1) Can be created by user when calling dispatchAction.
 * (2) Can be created by `BrushController`
 * for brush behavior. area params are picked from `cover.__brushOptoin`.
 * In `BrushController`, "covers" are create or updated for each "area".
 */
export interface BrushAreaParam extends ModelFinderObject {
    brushType: BrushCoverConfig['brushType'];
    id?: BrushCoverConfig['id'];
    range?: BrushCoverConfig['range'];
    panelId?: BrushCoverConfig['panelId'];
    coordRange?: BrushAreaRange;
    coordRanges?: BrushAreaRange[];
    __rangeOffset?: {
        offset: BrushDimensionMinMax[] | BrushDimensionMinMax;
        xyMinMax: BrushDimensionMinMax[];
    };
}
/**
 * Generated by `brushModel.setAreas`, which merges
 * `area: BrushAreaParam` and `brushModel.option: BrushOption`.
 * See `generateBrushOption`.
 */
export interface BrushAreaParamInternal extends BrushAreaParam {
    brushMode: BrushMode;
    brushStyle: BrushCoverConfig['brushStyle'];
    transformable: BrushCoverConfig['transformable'];
    removeOnClick: BrushCoverConfig['removeOnClick'];
    z: BrushCoverConfig['z'];
    __rangeOffset?: {
        offset: BrushDimensionMinMax | BrushDimensionMinMax[];
        xyMinMax: BrushDimensionMinMax[];
    };
}
export declare type BrushToolboxIconType = BrushType | 'keep' | 'clear';
export interface BrushOption extends ComponentOption, ModelFinderObject {
    mainType?: 'brush';
    toolbox?: BrushToolboxIconType[];
    brushLink?: number[] | 'all' | 'none';
    throttleType?: 'fixRate' | 'debounce';
    throttleDelay?: number;
    inBrush?: VisualOptionFixed;
    outOfBrush?: VisualOptionFixed;
    brushType?: BrushTypeUncertain;
    brushStyle?: {
        borderWidth?: number;
        color?: ZRColor;
        borderColor?: ZRColor;
    };
    transformable?: boolean;
    brushMode?: BrushMode;
    removeOnClick?: boolean;
}
declare class BrushModel extends ComponentModel<BrushOption> {
    static type: "brush";
    type: "brush";
    static dependencies: string[];
    static defaultOption: BrushOption;
    /**
     * @readOnly
     */
    areas: BrushAreaParamInternal[];
    /**
     * Current activated brush type.
     * If null, brush is inactived.
     * see module:echarts/component/helper/BrushController
     * @readOnly
     */
    brushType: BrushTypeUncertain;
    /**
     * Current brush painting area settings.
     * @readOnly
     */
    brushOption: BrushCoverCreatorConfig;
    brushTargetManager: BrushTargetManager;
    optionUpdated(newOption: BrushOption, isInit: boolean): void;
    /**
     * If `areas` is null/undefined, range state remain.
     */
    setAreas(areas?: BrushAreaParam[]): void;
    /**
     * Set the current painting brush option.
     */
    setBrushOption(brushOption: BrushCoverCreatorConfig): void;
}
export default BrushModel;
