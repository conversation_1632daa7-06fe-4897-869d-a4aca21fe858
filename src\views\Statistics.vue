<template>
  <div class="statistics">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon" style="background-color: #409EFF;">
              <el-icon size="24"><Tools /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalImprovements }}</div>
              <div class="stat-label">总改进项</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon" style="background-color: #67C23A;">
              <el-icon size="24"><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ closedImprovements }}</div>
              <div class="stat-label">已关闭</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon" style="background-color: #E6A23C;">
              <el-icon size="24"><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ inProgressImprovements }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon" style="background-color: #F56C6C;">
              <el-icon size="24"><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ overdueImprovements }}</div>
              <div class="stat-label">逾期项</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>改进项问题分类分布</span>
              <el-button size="small" @click="exportChart('category')">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </template>
          <div ref="categoryChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>各部门改进任务数量</span>
              <el-button size="small" @click="exportChart('department')">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </template>
          <div ref="departmentChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>改进项处理时效趋势</span>
              <el-button size="small" @click="exportChart('trend')">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </template>
          <div ref="trendChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>已关闭 vs 逾期改进项对比</span>
              <el-button size="small" @click="exportChart('comparison')">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </template>
          <div ref="comparisonChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 报表导出 -->
    <el-card class="export-section">
      <template #header>
        <span>报表导出</span>
      </template>
      
      <div class="export-controls">
        <el-form :model="exportForm" inline>
          <el-form-item label="报表类型">
            <el-select v-model="exportForm.type" placeholder="请选择报表类型">
              <el-option label="改进项统计报表" value="improvement" />
              <el-option label="部门绩效报表" value="department" />
              <el-option label="趋势分析报表" value="trend" />
              <el-option label="综合分析报表" value="comprehensive" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="exportForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          
          <el-form-item label="导出格式">
            <el-select v-model="exportForm.format" placeholder="请选择格式">
              <el-option label="Excel" value="excel" />
              <el-option label="PDF" value="pdf" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出报表
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const categoryChartRef = ref()
const departmentChartRef = ref()
const trendChartRef = ref()
const comparisonChartRef = ref()

// 统计数据
const totalImprovements = ref(25)
const closedImprovements = ref(15)
const inProgressImprovements = ref(7)
const overdueImprovements = ref(3)

// 导出表单
const exportForm = reactive({
  type: '',
  dateRange: [],
  format: 'excel'
})

// 图表实例
let categoryChart = null
let departmentChart = null
let trendChart = null
let comparisonChart = null

// 初始化问题分类饼图
const initCategoryChart = () => {
  categoryChart = echarts.init(categoryChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '问题分类',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 8, name: '质量问题' },
          { value: 6, name: '流程优化' },
          { value: 4, name: '成本控制' },
          { value: 4, name: '客户服务' },
          { value: 3, name: '安全环保' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  categoryChart.setOption(option)
}

// 初始化部门任务数量柱状图
const initDepartmentChart = () => {
  departmentChart = echarts.init(departmentChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['生产部', '质量部', '技术部', '采购部', '客服部', '环安部'],
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '改进任务数',
        type: 'bar',
        barWidth: '60%',
        data: [8, 6, 4, 3, 2, 2],
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }
  departmentChart.setOption(option)
}

// 初始化趋势图
const initTrendChart = () => {
  trendChart = echarts.init(trendChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增改进项', '已关闭改进项']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '新增改进项',
        type: 'line',
        stack: 'Total',
        data: [5, 3, 4, 6, 4, 3],
        itemStyle: {
          color: '#E6A23C'
        }
      },
      {
        name: '已关闭改进项',
        type: 'line',
        stack: 'Total',
        data: [2, 4, 3, 5, 3, 4],
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }
  trendChart.setOption(option)
}

// 初始化对比图
const initComparisonChart = () => {
  comparisonChart = echarts.init(comparisonChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['已关闭', '逾期']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: ['质量问题', '流程优化', '成本控制', '客户服务', '安全环保']
    },
    series: [
      {
        name: '已关闭',
        type: 'bar',
        data: [6, 4, 3, 3, 2],
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '逾期',
        type: 'bar',
        data: [1, 1, 0, 1, 0],
        itemStyle: {
          color: '#F56C6C'
        }
      }
    ]
  }
  comparisonChart.setOption(option)
}

// 导出图表
const exportChart = (type) => {
  let chart = null
  let filename = ''
  
  switch (type) {
    case 'category':
      chart = categoryChart
      filename = '改进项问题分类分布图'
      break
    case 'department':
      chart = departmentChart
      filename = '各部门改进任务数量图'
      break
    case 'trend':
      chart = trendChart
      filename = '改进项处理时效趋势图'
      break
    case 'comparison':
      chart = comparisonChart
      filename = '已关闭vs逾期改进项对比图'
      break
  }
  
  if (chart) {
    const url = chart.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.href = url
    link.download = `${filename}.png`
    link.click()
    
    ElMessage.success(`${filename} 导出成功`)
  }
}

// 导出报表
const handleExport = () => {
  if (!exportForm.type) {
    ElMessage.warning('请选择报表类型')
    return
  }
  
  if (!exportForm.dateRange || exportForm.dateRange.length === 0) {
    ElMessage.warning('请选择时间范围')
    return
  }
  
  const reportTypeMap = {
    'improvement': '改进项统计报表',
    'department': '部门绩效报表',
    'trend': '趋势分析报表',
    'comprehensive': '综合分析报表'
  }
  
  const formatMap = {
    'excel': 'Excel',
    'pdf': 'PDF'
  }
  
  ElMessage.success(`正在导出${reportTypeMap[exportForm.type]}(${formatMap[exportForm.format]}格式)...`)
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  categoryChart?.resize()
  departmentChart?.resize()
  trendChart?.resize()
  comparisonChart?.resize()
}

onMounted(() => {
  nextTick(() => {
    initCategoryChart()
    initDepartmentChart()
    initTrendChart()
    initComparisonChart()
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  })
})
</script>

<style scoped>
.statistics {
  padding: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.charts-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.export-section {
  margin-top: 20px;
}

.export-controls {
  padding: 10px 0;
}
</style>
